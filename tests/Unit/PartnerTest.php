<?php

use App\Models\Partner;

describe('Partner model', function () {
    it('can create a partner', function () {
        $partner = Partner::factory()->create();

        expect($partner->id)->not->toBeNull();
        expect($partner->company_name)->not->toBeNull();
        expect($partner->email)->not->toBeNull();
        expect($partner->phone)->not->toBeNull();
        expect(Partner::where('id', $partner->id)->exists())->toBeTrue();
    });

    it('has fillable attributes', function () {
        $partner = new Partner;
        expect($partner->getFillable())->toEqual([
            'company_name',
            'email',
            'phone',
        ]);
    });
});
