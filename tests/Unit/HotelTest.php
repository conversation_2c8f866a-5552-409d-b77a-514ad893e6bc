<?php

use App\Models\Hotel;
use App\Models\Room;
use App\Models\RoomType;

describe('Hotel model', function () {
    it('can create a hotel with fillable attributes', function () {
        $hotel = Hotel::factory()->create([
            'name' => 'Test Hotel',
            'address' => '123 Main St',
            'city' => 'Test City',
            'country' => 'Test Country',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '1234567890',
        ]);

        expect($hotel->name)->toBe('Test Hotel');
        expect($hotel->address)->toBe('123 Main St');
        expect($hotel->city)->toBe('Test City');
        expect($hotel->country)->toBe('Test Country');
        expect($hotel->contact_email)->toBe('<EMAIL>');
        expect($hotel->contact_phone)->toBe('1234567890');
    });

    it('can have many room types', function () {
        $hotel = Hotel::factory()->create();
        $roomTypes = RoomType::factory()->count(3)->create(['hotel_id' => $hotel->id]);
        $hotelRoomTypes = $hotel->roomTypes;
        expect($hotelRoomTypes)->toHaveCount(3);
        expect($hotelRoomTypes->pluck('id')->sort()->values())->toEqual($roomTypes->pluck('id')->sort()->values());
    });

    it('can have many rooms through room types', function () {
        $hotel = Hotel::factory()->create();
        $roomTypes = RoomType::factory()->count(2)->create(['hotel_id' => $hotel->id]);
        foreach ($roomTypes as $roomType) {
            Room::factory()->count(2)->create(['room_type_id' => $roomType->id]);
        }
        $rooms = $hotel->rooms;
        expect($rooms)->toHaveCount(4);
        expect($rooms->pluck('room_type_id')->unique()->sort()->values())->toEqual($roomTypes->pluck('id')->sort()->values());
    });
});
