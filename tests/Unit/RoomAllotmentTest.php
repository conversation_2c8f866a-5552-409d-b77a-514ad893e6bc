<?php

use App\Models\Hotel;
use App\Models\Partner;
use App\Models\RoomAllotment;
use App\Models\RoomType;

describe('RoomAllotment model', function () {
    beforeEach(function () {
        $this->hotel = Hotel::factory()->create();
        $this->roomType = RoomType::factory()->create(['hotel_id' => $this->hotel->id]);
        $this->partner = Partner::factory()->create();
    });

    it('can be created with fillable attributes', function () {
        $allotment = RoomAllotment::create([
            'hotel_id' => $this->hotel->id,
            'room_type_id' => $this->roomType->id,
            'partner_id' => $this->partner->id,
            'total_rooms' => 10,
            'blocked_rooms' => 2,
            'used_rooms' => 3,
        ]);

        expect($allotment)->toBeInstanceOf(RoomAllotment::class)
            ->and($allotment->hotel_id)->toBe($this->hotel->id)
            ->and($allotment->room_type_id)->toBe($this->roomType->id)
            ->and($allotment->partner_id)->toBe($this->partner->id)
            ->and($allotment->total_rooms)->toBe(10)
            ->and($allotment->blocked_rooms)->toBe(2)
            ->and($allotment->used_rooms)->toBe(3)
            ->and($allotment->date)->not()->toBeNull();
    });

    it('sets the date to today if not provided', function () {
        $allotment = RoomAllotment::create([
            'hotel_id' => $this->hotel->id,
            'room_type_id' => $this->roomType->id,
            'partner_id' => $this->partner->id,
            'total_rooms' => 5,
            'blocked_rooms' => 1,
            'used_rooms' => 1,
        ]);

        expect($allotment->date->toDateString())->toEqual(today()->toDateString());
    });

    it('belongs to a hotel', function () {
        $allotment = RoomAllotment::factory()->create([
            'hotel_id' => $this->hotel->id,
            'room_type_id' => $this->roomType->id,
            'partner_id' => $this->partner->id,
        ]);
        expect($allotment->hotel)->toBeInstanceOf(Hotel::class);
    });

    it('belongs to a room type', function () {
        $allotment = RoomAllotment::factory()->create([
            'hotel_id' => $this->hotel->id,
            'room_type_id' => $this->roomType->id,
            'partner_id' => $this->partner->id,
        ]);
        expect($allotment->roomType)->toBeInstanceOf(RoomType::class);
    });

    it('belongs to a partner', function () {
        $allotment = RoomAllotment::factory()->create([
            'hotel_id' => $this->hotel->id,
            'room_type_id' => $this->roomType->id,
            'partner_id' => $this->partner->id,
        ]);
        expect($allotment->partner)->toBeInstanceOf(Partner::class);
    });
});
