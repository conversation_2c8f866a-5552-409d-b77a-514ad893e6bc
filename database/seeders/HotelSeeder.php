<?php

namespace Database\Seeders;

use App\Models\Hotel;
use App\Models\Room;
use App\Models\RoomType;
use Illuminate\Database\Seeder;

class HotelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create hotels with room types and rooms
        Hotel::factory(3)
            ->has(
                RoomType::factory(4)
                    ->has(Room::factory(5))
            )
            ->create();

        // Example of creating a specific hotel with specific room types
        $hotel = Hotel::factory()->create([
            'name' => 'Grand Plaza Hotel',
            'city' => 'New York',
            'country' => 'USA',
        ]);

        // Create room types for this specific hotel
        $roomTypes = [
            'Standard' => 2,
            'Deluxe' => 4,
            'Suite' => 6,
        ];

        foreach ($roomTypes as $name => $capacity) {
            $roomType = RoomType::factory()->create([
                'hotel_id' => $hotel->id,
                'name' => $name,
                'capacity' => $capacity,
            ]);

            // Create 3 rooms for each room type
            Room::factory(3)->create([
                'room_type_id' => $roomType->id,
            ]);

            // Create 1 room under maintenance for each room type
            Room::factory()->maintenance()->create([
                'room_type_id' => $roomType->id,
            ]);
        }
    }
}
