<?php

namespace Database\Seeders;

use App\Models\Partner;
use Illuminate\Database\Seeder;

class PartnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 10 random partners
        Partner::factory(10)->create();

        // Create some specific partners for testing
        Partner::factory()->create([
            'company_name' => 'Travel Solutions Inc.',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
        ]);

        Partner::factory()->create([
            'company_name' => 'Global Tourism Partners',
            'email' => '<EMAIL>',
            'phone' => '******-0456',
        ]);

        Partner::factory()->create([
            'company_name' => 'Hospitality Connect',
            'email' => '<EMAIL>',
            'phone' => '******-0789',
        ]);
    }
}
