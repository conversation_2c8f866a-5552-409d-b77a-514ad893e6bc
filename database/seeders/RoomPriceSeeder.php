<?php

namespace Database\Seeders;

use App\Models\Currency;
use App\Models\Hotel;
use App\Models\RoomBasePrice;
use App\Models\RoomSellingPrice;
use Illuminate\Database\Seeder;

class RoomPriceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currencies = Currency::all();
        $hotels = Hotel::with('roomTypes')->get();

        foreach ($hotels as $hotel) {
            foreach ($hotel->roomTypes as $roomType) {
                $currency = $currencies->random();
                RoomBasePrice::factory()->create([
                    'hotel_id' => $hotel->id,
                    'room_type_id' => $roomType->id,
                    'currency_id' => $currency->id,
                ]);
                RoomSellingPrice::factory()->create([
                    'hotel_id' => $hotel->id,
                    'room_type_id' => $roomType->id,
                    'currency_id' => $currency->id,
                ]);
            }
        }
    }
}
