<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Seeder;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Currency::query()->updateOrCreate([
            'code' => 'IDR',
        ], [
            'symbol' => 'Rp',
            'rate' => 1,
            'is_default' => true,
        ]);
        Currency::query()->updateOrCreate([
            'code' => 'SAR',
        ], [
            'symbol' => '﷼',
            'rate' => 4300,
            'is_default' => false,
        ]);
        Currency::query()->updateOrCreate([
            'code' => 'USD',
        ], [
            'symbol' => '$',
            'rate' => 16200,
            'is_default' => false,
        ]);
    }
}
