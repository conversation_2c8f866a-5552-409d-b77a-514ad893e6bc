<?php

namespace Database\Seeders;

use App\Models\Hotel;
use App\Models\Partner;
use App\Models\RoomAllotment;
use Illuminate\Database\Seeder;

class RoomAllotmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing hotels and partners
        $hotels = Hotel::with('roomTypes')->get();
        $partners = Partner::all();

        if ($hotels->isEmpty()) {
            $this->command->warn('No hotels found. Please seed hotels first.');

            return;
        }

        // Create room allotments for each hotel and room type combination
        foreach ($hotels as $hotel) {
            foreach ($hotel->roomTypes as $roomType) {
                // Create allotments for the next 7 days
                for ($i = 0; $i < 7; $i++) {
                    $date = now()->addDays($i);

                    // Skip some days for variety (simulate business patterns)
                    if ($i % 7 === 0 && fake()->boolean(30)) {
                        continue;
                    }

                    // Randomly assign partner (70% chance) or leave null (30% chance)
                    $partnerId = null;
                    if ($partners->isNotEmpty() && fake()->boolean(70)) {
                        $partnerId = $partners->random()->id;
                    }

                    RoomAllotment::factory()
                        ->forHotelAndRoomType($hotel, $roomType)
                        ->create([
                            'partner_id' => $partnerId,
                            'date' => $date,
                        ]);
                }
            }
        }

        // Create some specific scenarios for testing
        if ($hotels->isNotEmpty()) {
            $firstHotel = $hotels->first();
            $firstRoomType = $firstHotel->roomTypes->first();

            if ($firstRoomType) {
                // Create a fully booked allotment for today (with partner)
                $partnerId = $partners->isNotEmpty() ? $partners->first()->id : null;
                RoomAllotment::factory()
                    ->forHotelAndRoomType($firstHotel, $firstRoomType)
                    ->fullyBooked()
                    ->today()
                    ->create([
                        'partner_id' => $partnerId,
                    ]);

                // Create an empty allotment for tomorrow (without partner)
                RoomAllotment::factory()
                    ->forHotelAndRoomType($firstHotel, $firstRoomType)
                    ->empty()
                    ->create([
                        'partner_id' => null,
                        'date' => now()->addDay(),
                    ]);

                // Create some future allotments (mix of with/without partners)
                for ($i = 0; $i < 2; $i++) {
                    $partnerId = null;
                    if ($partners->isNotEmpty() && fake()->boolean(60)) {
                        $partnerId = $partners->random()->id;
                    }

                    RoomAllotment::factory()
                        ->forHotelAndRoomType($firstHotel, $firstRoomType)
                        ->future()
                        ->create([
                            'partner_id' => $partnerId,
                        ]);
                }
            }
        }
    }
}
