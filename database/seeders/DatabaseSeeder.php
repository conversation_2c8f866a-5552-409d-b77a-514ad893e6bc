<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        // User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);

        $this->call(CurrencySeeder::class);

        // Seed partners
        $this->call(PartnerSeeder::class);

        // Seed hotels with room types and rooms
        $this->call(HotelSeeder::class);

        // Seed room allotments after hotels, room types, and partners are created
        $this->call(RoomAllotmentSeeder::class);

        $this->call(RoomPriceSeeder::class);
    }
}
