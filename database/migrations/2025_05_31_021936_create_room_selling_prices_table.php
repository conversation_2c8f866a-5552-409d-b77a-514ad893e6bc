<?php

use App\Models\Currency;
use App\Models\Hotel;
use App\Models\Partner;
use App\Models\RoomType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('room_selling_prices', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(Hotel::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(RoomType::class)->constrained()->cascadeOnDelete();
            $table->enum('customer_type', ['b2b', 'b2c'])->default('b2c');
            $table->foreignIdFor(Partner::class)->nullable()->constrained()->nullOnDelete();
            $table->string('season')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->decimal('selling_price', 15);
            $table->foreignIdFor(Currency::class)->nullable()->constrained()->nullOnDelete();
            $table->enum('markup_type', ['flat', 'percent'])->default('flat');
            $table->decimal('markup_value', 15)->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('room_selling_prices');
    }
};
