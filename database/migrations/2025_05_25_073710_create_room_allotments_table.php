<?php

use App\Models\Hotel;
use App\Models\Partner;
use App\Models\RoomType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('room_allotments', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(Hotel::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(RoomType::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Partner::class)->nullable()->constrained()->nullOnDelete();

            $table->date('date');
            $table->integer('total_rooms')->default(1);
            $table->integer('blocked_rooms')->default(0);
            $table->integer('used_rooms')->default(0);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('room_allotments');
    }
};
