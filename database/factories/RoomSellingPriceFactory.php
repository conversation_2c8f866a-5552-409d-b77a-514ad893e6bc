<?php

namespace Database\Factories;

use App\Enums\MarkupType;
use App\Models\Currency;
use App\Models\Hotel;
use App\Models\Partner;
use App\Models\RoomSellingPrice;
use App\Models\RoomType;
use Illuminate\Database\Eloquent\Factories\Factory;

class RoomSellingPriceFactory extends Factory
{
    protected $model = RoomSellingPrice::class;

    public function definition()
    {
        $seasons = ['low', 'high', 'ramadan'];
        $customerTypes = ['b2b', 'b2c'];
        $startDate = $this->faker->dateTimeBetween('-1 year', '+1 month');
        $endDate = (clone $startDate)->modify('+7 days');

        return [
            // We'll set hotel_id and room_type_id in configure()
            'hotel_id' => null,
            'room_type_id' => null,
            'customer_type' => $this->faker->randomElement($customerTypes),
            'partner_id' => Partner::factory(),
            'season' => $this->faker->randomElement($seasons),
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'selling_price' => $this->faker->randomFloat(2, 100, 2000),
            'currency_id' => Currency::first() ?? Currency::factory(),
            'markup_type' => $this->faker->randomElement(MarkupType::cases()),
            'markup_value' => $this->faker->randomFloat(2, 0, 100),
        ];
    }

    public function configure()
    {
        return $this->afterMaking(function (RoomSellingPrice $rsp) {
            if (! $rsp->hotel_id) {
                $hotel = Hotel::factory()->create();
                $rsp->hotel_id = $hotel->id;
            } else {
                $hotel = Hotel::find($rsp->hotel_id);
            }
            $roomType = RoomType::factory()->create(['hotel_id' => $rsp->hotel_id]);
            $rsp->room_type_id = $roomType->id;
        })->afterCreating(function (RoomSellingPrice $rsp) {
            if (! $rsp->hotel_id) {
                $hotel = Hotel::factory()->create();
                $rsp->hotel_id = $hotel->id;
                $rsp->save();
            } else {
                $hotel = Hotel::find($rsp->hotel_id);
            }
            $roomType = RoomType::factory()->create(['hotel_id' => $rsp->hotel_id]);
            $rsp->room_type_id = $roomType->id;
            $rsp->save();
        });
    }
}
