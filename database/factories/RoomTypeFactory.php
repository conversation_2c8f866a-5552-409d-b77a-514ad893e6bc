<?php

namespace Database\Factories;

use App\Models\Hotel;
use App\Models\RoomType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RoomType>
 */
class RoomTypeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = RoomType::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $roomTypes = [
            'Standard' => 'A comfortable room with essential amenities',
            'Deluxe' => 'A spacious room with premium furnishings and amenities',
            'Suite' => 'A luxurious suite with separate living area and premium amenities',
            'Family' => 'A spacious room designed for families with additional beds',
            'Executive' => 'A premium room with exclusive access to executive lounge',
            'Presidential' => 'The most luxurious accommodation with exceptional amenities',
            'Single' => 'A cozy room designed for single occupancy',
            'Double' => 'A comfortable room with a double bed',
            'Twin' => 'A room with two single beds',
        ];

        $roomType = fake()->randomElement(array_keys($roomTypes));
        $description = $roomTypes[$roomType];
        
        return [
            'hotel_id' => Hotel::factory(),
            'name' => $roomType . ' Room',
            'description' => $description,
            'capacity' => fake()->numberBetween(1, 6),
            'created_at' => fake()->dateTimeBetween('-1 year', 'now'),
            'updated_at' => function (array $attributes) {
                return fake()->dateTimeBetween($attributes['created_at'], 'now');
            },
        ];
    }
}
