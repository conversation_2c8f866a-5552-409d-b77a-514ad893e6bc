<?php

namespace Database\Factories;

use App\Models\Hotel;
use App\Models\Partner;
use App\Models\RoomAllotment;
use App\Models\RoomType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RoomAllotment>
 */
class RoomAllotmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = RoomAllotment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $totalRooms = fake()->numberBetween(10, 50);
        $blockedRooms = fake()->numberBetween(0, (int) ($totalRooms * 0.3)); // Max 30% blocked
        $usedRooms = fake()->numberBetween(0, $totalRooms - $blockedRooms); // Can't exceed available rooms

        return [
            'hotel_id' => Hotel::factory(),
            'room_type_id' => RoomType::factory(),
            'partner_id' => fake()->boolean(70) ? Partner::factory() : null, // 70% chance of having a partner
            'date' => fake()->dateTimeBetween('-30 days', '+60 days'),
            'total_rooms' => $totalRooms,
            'blocked_rooms' => $blockedRooms,
            'used_rooms' => $usedRooms,
            'created_at' => fake()->dateTimeBetween('-1 year', 'now'),
            'updated_at' => function (array $attributes) {
                return fake()->dateTimeBetween($attributes['created_at'], 'now');
            },
        ];
    }

    /**
     * Create a room allotment for a specific hotel and room type combination.
     */
    public function forHotelAndRoomType(Hotel $hotel, RoomType $roomType): static
    {
        return $this->state(fn (array $attributes) => [
            'hotel_id' => $hotel->id,
            'room_type_id' => $roomType->id,
        ]);
    }

    /**
     * Create a fully booked room allotment.
     */
    public function fullyBooked(): static
    {
        return $this->state(function (array $attributes) {
            $totalRooms = $attributes['total_rooms'];
            $blockedRooms = $attributes['blocked_rooms'];

            return [
                'used_rooms' => $totalRooms - $blockedRooms,
            ];
        });
    }

    /**
     * Create a room allotment with no bookings.
     */
    public function empty(): static
    {
        return $this->state(fn (array $attributes) => [
            'used_rooms' => 0,
            'blocked_rooms' => 0,
        ]);
    }

    /**
     * Create a room allotment for today's date.
     */
    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'date' => today(),
        ]);
    }

    /**
     * Create a room allotment for a future date.
     */
    public function future(): static
    {
        return $this->state(fn (array $attributes) => [
            'date' => fake()->dateTimeBetween('+1 day', '+90 days'),
        ]);
    }

    /**
     * Use existing hotels and room types instead of creating new ones.
     */
    public function useExisting(): static
    {
        return $this->state(function (array $attributes) {
            // Get a random existing hotel with its room types
            $hotel = Hotel::with('roomTypes')->inRandomOrder()->first();

            if (! $hotel || $hotel->roomTypes->isEmpty()) {
                // Fallback to creating new ones if none exist
                $hotel = Hotel::factory()->create();
                $roomType = RoomType::factory()->create(['hotel_id' => $hotel->id]);
            } else {
                $roomType = $hotel->roomTypes->random();
            }

            return [
                'hotel_id' => $hotel->id,
                'room_type_id' => $roomType->id,
            ];
        });
    }
}
