<?php

namespace Database\Factories;

use App\Models\Currency;
use App\Models\Hotel;
use App\Models\RoomBasePrice;
use App\Models\RoomType;
use Illuminate\Database\Eloquent\Factories\Factory;

class RoomBasePriceFactory extends Factory
{
    protected $model = RoomBasePrice::class;

    public function definition()
    {
        $seasons = ['high', 'low', 'ramadan'];
        $sources = ['manual', 'excel', 'api'];

        $startDate = $this->faker->dateTimeBetween('-1 year', '+1 month');
        $endDate = (clone $startDate)->modify('+7 days');

        return [
            // We'll set hotel_id and room_type_id in configure()
            'hotel_id' => null,
            'room_type_id' => null,
            'season' => $this->faker->randomElement($seasons),
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'base_price' => $this->faker->randomFloat(2, 100, 1000),
            'currency_id' => Currency::first() ?? Currency::factory(),
            'source' => $this->faker->randomElement($sources),
        ];
    }

    public function configure()
    {
        return $this->afterMaking(function (RoomBasePrice $rbp) {
            if (! $rbp->hotel_id) {
                $hotel = Hotel::factory()->create();
                $rbp->hotel_id = $hotel->id;
            } else {
                $hotel = Hotel::find($rbp->hotel_id);
            }
            $roomType = RoomType::factory()->create(['hotel_id' => $rbp->hotel_id]);
            $rbp->room_type_id = $roomType->id;
        })->afterCreating(function (RoomBasePrice $rbp) {
            if (! $rbp->hotel_id) {
                $hotel = Hotel::factory()->create();
                $rbp->hotel_id = $hotel->id;
                $rbp->save();
            } else {
                $hotel = Hotel::find($rbp->hotel_id);
            }
            $roomType = RoomType::factory()->create(['hotel_id' => $rbp->hotel_id]);
            $rbp->room_type_id = $roomType->id;
            $rbp->save();
        });
    }
}
