<?php

namespace Database\Factories;

use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

class CurrencyFactory extends Factory
{
    protected $model = Currency::class;

    public function definition()
    {
        $codes = ['USD', 'EUR', 'SAR', 'IDR', 'MYR'];
        $symbols = ['$', '€', '﷼', 'Rp', 'RM'];
        $index = $this->faker->numberBetween(0, count($codes) - 1);

        return [
            'code' => $codes[$index],
            'symbol' => $symbols[$index],
            'rate' => $this->faker->randomFloat(4, 0.1, 10),
            'is_default' => $this->faker->boolean(10), // 10% chance to be default
        ];
    }
}
