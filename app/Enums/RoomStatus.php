<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum RoomStatus: string implements HasColor, HasLabel
{
    case Available = 'available';
    case Booked = 'booked';
    case Confirmed = 'confirmed';
    case CheckedIn = 'checked-in';
    case CheckedOut = 'checked-out';
    case Maintenance = 'maintenance';
    case Cancelled = 'cancelled';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Available => 'Available',
            self::Booked => 'Booked',
            self::Confirmed => 'Confirmed',
            self::CheckedIn => 'Checked-In',
            self::CheckedOut => 'Checked-Out',
            self::Maintenance => 'Maintenance',
            self::Cancelled => 'Cancelled',
        };
    }

    public function getColor(): ?string
    {
        return match ($this) {
            self::Available => 'success',
            self::Booked => 'warning',
            self::Confirmed => 'primary',
            self::CheckedIn => 'info',
            self::CheckedOut => 'secondary',
            self::Maintenance => 'danger',
            self::Cancelled => 'gray',
        };
    }
}
