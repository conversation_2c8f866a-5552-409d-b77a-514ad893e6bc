<?php

namespace App\Models;

use App\Enums\MarkupType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RoomSellingPrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'hotel_id',
        'room_type_id',
        'customer_type', // b2b / b2c
        'partner_id',
        'season', // low, high, ramadan, etc
        'start_date',
        'end_date',
        'selling_price',
        'currency_id',
        'markup_type',
        'markup_value',
    ];

    protected function casts()
    {
        return [
            // 'season' => RoomPriceSeason::class,
            'start_date' => 'date',
            'end_date' => 'date',
            'selling_price' => 'float',
            'markup_type' => MarkupType::class,
            'markup_value' => 'float',
        ];
    }

    public function hotel(): BelongsTo
    {
        return $this->belongsTo(Hotel::class);
    }

    public function roomType(): BelongsTo
    {
        return $this->belongsTo(RoomType::class);
    }

    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class);
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }
}
