<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'symbol',
        'rate',
        'is_default',
    ];

    protected function casts()
    {
        return [
            'rate' => 'float',
            'is_default' => 'bool',
        ];
    }
}
