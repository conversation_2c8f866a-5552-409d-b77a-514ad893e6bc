<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RoomBasePrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'hotel_id',
        'room_type_id',
        'season', // high, low, ramadan, etc
        'start_date',
        'end_date',
        'base_price',
        'currency_id',
        'source', // manual, excel, api
    ];

    protected function casts()
    {
        return [
            // 'season' => RoomPriceSeason::class,
            'start_date' => 'date',
            'end_date' => 'date',
            'base_price' => 'float',
            // 'source' => RoomPriceSource::class,
        ];
    }

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->source ??= 'manual';
        });
    }

    public function hotel(): BelongsTo
    {
        return $this->belongsTo(Hotel::class);
    }

    public function roomType(): BelongsTo
    {
        return $this->belongsTo(RoomType::class);
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }
}
