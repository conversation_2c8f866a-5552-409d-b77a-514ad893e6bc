<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RoomAllotmentResource\Pages;
use App\Models\RoomAllotment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class RoomAllotmentResource extends Resource
{
    protected static ?string $model = RoomAllotment::class;

    protected static ?string $navigationIcon = 'lucide-grid-2x2-check';

    protected static ?string $navigationGroup = 'Hotels & Rooms';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('hotel_id')
                    ->relationship('hotel', 'name')
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($set) {
                        $set('room_type_id', null);
                    }),
                Forms\Components\Select::make('room_type_id')
                    ->relationship('roomType', 'name', fn ($get, $query) => $query->where('hotel_id', $get('hotel_id')))
                    ->required(),
                Forms\Components\Select::make('partner_id')
                    ->relationship('partner', 'id')
                    ->required(),
                Forms\Components\DatePicker::make('date')
                    ->required(),
                Forms\Components\TextInput::make('total_rooms')
                    ->required()
                    ->numeric()
                    ->default(1),
                Forms\Components\TextInput::make('blocked_rooms')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('used_rooms')
                    ->required()
                    ->numeric()
                    ->default(0),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('hotel.name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('roomType.name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('partner.company_name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_rooms')
                    ->label('Total')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('blocked_rooms')
                    ->label('Blocked')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('used_rooms')
                    ->label('Used')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('hotel')
                    ->relationship('hotel', 'name')
                    ->multiple()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRoomAllotments::route('/'),
        ];
    }
}
