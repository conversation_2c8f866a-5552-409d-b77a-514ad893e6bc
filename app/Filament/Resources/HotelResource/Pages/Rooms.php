<?php

namespace App\Filament\Resources\HotelResource\Pages;

use App\Enums\RoomStatus;
use App\Filament\Resources\HotelResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;

class Rooms extends ManageRelatedRecords
{
    protected static string $resource = HotelResource::class;

    protected static string $relationship = 'rooms';

    protected static ?string $navigationIcon = 'lucide-door-open';

    protected static ?string $navigationLabel = 'Rooms';

    protected static ?string $breadcrumb = 'Rooms';

    public function getTitle(): string | Htmlable
    {
        return $this->getRecordTitle();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('room_number')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('room_type_id')
                    ->relationship('roomType', 'name', function ($query) {
                        return $query->where('hotel_id', $this->getOwnerRecord()->id);
                    })
                    ->required()
                    ->preload(),
                Forms\Components\ToggleButtons::make('status')
                    ->options(RoomStatus::class)
                    ->required()
                    ->default(RoomStatus::Available)
                    ->inline()
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('room_number')
                    ->sortable(),
                Tables\Columns\TextColumn::make('roomType.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ])
            ->defaultSort('room_number');
    }
}
