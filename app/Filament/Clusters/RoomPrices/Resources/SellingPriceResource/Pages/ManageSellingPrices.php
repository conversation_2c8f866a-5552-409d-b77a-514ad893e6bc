<?php

namespace App\Filament\Clusters\RoomPrices\Resources\SellingPriceResource\Pages;

use App\Filament\Clusters\RoomPrices\Resources\SellingPriceResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageSellingPrices extends ManageRecords
{
    protected static string $resource = SellingPriceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
