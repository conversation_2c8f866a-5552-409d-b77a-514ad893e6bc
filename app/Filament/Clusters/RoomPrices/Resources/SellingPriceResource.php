<?php

namespace App\Filament\Clusters\RoomPrices\Resources;

use App\Filament\Clusters\RoomPrices;
use App\Filament\Clusters\RoomPrices\Resources\SellingPriceResource\Pages;
use App\Models\RoomSellingPrice;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class SellingPriceResource extends Resource
{
    protected static ?string $cluster = RoomPrices::class;

    protected static ?string $model = RoomSellingPrice::class;

    protected static ?string $modelLabel = 'selling price';

    protected static ?string $navigationIcon = 'lucide-coins';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('hotel_id')
                    ->relationship('hotel', 'name')
                    ->required(),
                Forms\Components\Select::make('room_type_id')
                    ->relationship('roomType', 'name')
                    ->required(),
                Forms\Components\TextInput::make('customer_type')
                    ->required(),
                Forms\Components\Select::make('partner_id')
                    ->relationship('partner', 'id')
                    ->default(null),
                Forms\Components\TextInput::make('season')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\DatePicker::make('start_date'),
                Forms\Components\DatePicker::make('end_date'),
                Forms\Components\TextInput::make('selling_price')
                    ->required()
                    ->numeric(),
                Forms\Components\Select::make('currency_id')
                    ->relationship('currency', 'id')
                    ->default(null),
                Forms\Components\TextInput::make('markup_type')
                    ->required(),
                Forms\Components\TextInput::make('markup_value')
                    ->numeric()
                    ->default(null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('hotel.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('roomType.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('customer_type'),
                Tables\Columns\TextColumn::make('partner.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('season')
                    ->searchable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('selling_price')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('currency.id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('markup_type'),
                Tables\Columns\TextColumn::make('markup_value')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageSellingPrices::route('/'),
        ];
    }
}
