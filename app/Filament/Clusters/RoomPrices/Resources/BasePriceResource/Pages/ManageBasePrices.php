<?php

namespace App\Filament\Clusters\RoomPrices\Resources\BasePriceResource\Pages;

use App\Filament\Clusters\RoomPrices\Resources\BasePriceResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageBasePrices extends ManageRecords
{
    protected static string $resource = BasePriceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
