<?php

namespace App\Filament\Clusters\RoomPrices\Resources;

use App\Filament\Clusters\RoomPrices;
use App\Filament\Clusters\RoomPrices\Resources\BasePriceResource\Pages;
use App\Models\RoomBasePrice;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class BasePriceResource extends Resource
{
    protected static ?string $cluster = RoomPrices::class;

    protected static ?string $model = RoomBasePrice::class;

    protected static ?string $modelLabel = 'base price';

    protected static ?string $navigationIcon = 'lucide-coins';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('hotel_id')
                    ->relationship('hotel', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->live()
                    ->afterStateUpdated(fn ($set) => $set('room_type_id', null)),
                Forms\Components\Select::make('room_type_id')
                    ->relationship('roomType', 'name', fn ($get, $query) => $query->where('hotel_id', $get('hotel_id')))
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\TextInput::make('season')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\DatePicker::make('start_date'),
                Forms\Components\DatePicker::make('end_date'),
                Forms\Components\TextInput::make('base_price')
                    ->required()
                    ->numeric(),
                Forms\Components\Select::make('currency_id')
                    ->relationship('currency', 'code')
                    ->default(null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('hotel.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('roomType.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('season')
                    ->searchable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->date()
                    ->sortable()
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable()
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('base_price')
                    ->currency(fn ($record) => $record->currency?->code ?? 'IDR', true)
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\TextColumn::make('source')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('hotel')
                    ->relationship('hotel', 'name')
                    ->multiple()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageBasePrices::route('/'),
        ];
    }
}
