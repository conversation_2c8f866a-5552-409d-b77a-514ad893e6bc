<?php

namespace App\Providers;

use Filament\Actions;
use Filament\Forms;
use Filament\Panel;
use Filament\Support\Enums\Platform;
use Filament\Support\Facades\FilamentIcon;
use Filament\Support\Facades\FilamentView;
use Filament\Tables;
use Filament\View\PanelsRenderHook;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class ShadcnThemeProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        Panel::configureUsing(function (Panel $panel) {
            $panel
                ->sidebarWidth('16rem')
                ->sidebarCollapsibleOnDesktop()
                ->globalSearchKeyBindings(['command+k', 'ctrl+k'])
                ->globalSearchFieldSuffix(fn (): ?string => match (Platform::detect()) {
                    Platform::Windows, Platform::Linux => 'CTRL+K',
                    Platform::Mac => '⌘K',
                    default => null,
                });
            // ->breadcrumbs(false);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        FilamentIcon::register([
            'panels::global-search.field' => 'lucide-search',
            'panels::user-menu.profile-item' => 'lucide-circle-user-round',
            'panels::user-menu.logout-button' => 'lucide-log-out',
            'panels::pages.dashboard.navigation-item' => 'lucide-layout-dashboard',
            'panels::topbar.open-database-notifications-button' => 'lucide-bell',
            'actions::action-group' => 'lucide-ellipsis-vertical',
            'actions::view-action' => 'lucide-eye',
            'actions::edit-action' => 'lucide-square-pen',
            'actions::delete-action' => 'lucide-trash-2',
            'tables::search-field' => 'lucide-search',
            'tables::actions.filter' => 'lucide-list-filter',
            'tables::actions.toggle-columns' => 'lucide-columns-3',
            'tables::columns.icon-column.true' => 'lucide-circle-check',
            'tables::columns.icon-column.false' => 'lucide-circle-x',
        ]);
        FilamentView::registerRenderHook(
            PanelsRenderHook::SIDEBAR_NAV_START,
            fn () => Blade::render('@livewire(Filament\Livewire\GlobalSearch::class)')
        );
        FilamentView::registerRenderHook(
            PanelsRenderHook::SIDEBAR_FOOTER,
            fn () => view('filament.shadcn.sidebar.user-menu')
        );
        FilamentView::registerRenderHook(
            PanelsRenderHook::TOPBAR_START,
            fn () => view('filament.shadcn.topbar.start')
        );

        Forms\Components\Section::configureUsing(function ($section) {
            $section->compact();
        });

        Actions\CreateAction::configureUsing(function ($action) {
            $action->icon('lucide-plus');
        });

        Tables\Actions\CreateAction::configureUsing(function ($action) {
            $action->icon('lucide-plus');
        });
        Tables\Actions\ActionGroup::configureUsing(function ($action) {
            $action
                ->color('gray')
                ->size('sm');
        });
    }
}
